(deffacts estadoInicial
    (hice nada)             ; Último movimiento realizado
    (pasosDados 0)          ; Contador de pasos actual
    (pasosMax 1000)         ; Máximo de pasos permitidos
)

(defrule moverWilly "Mueve a Willy cuando no hay peligros"
    (directions $? ?direction $?)
    ?h <- (hice $?)
    (not (percepts $? Pull $?))         ; No hay agujero negro
    (not (percepts $? Noise $?))        ; No hay alienígena
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))        ; No excede pasos máximos
    =>
    (retract ?h ?pasos)
    (assert (hice ?direction))          ; Registra el movimiento
    (assert (pasosDados (+ ?n 1)))      ; Incrementa pasos
    (moveWilly ?direction)
)

(deffacts contadorPasos
    (pasosMax 100)          ; Define el máximo de pasos
    (pasosDados 0)          ; Inicializa el contador
)

(defrule fireWilly "Dispara en una dirección aleatoria cuando escucha un Alien"
	(hasLaser)
	(percepts $? Noise $?)
	(directions $? ?direction $?)
	=>
	(fireLaser ?direction)
)
    
(defrule volverNorthWilly "Retrocede del norte si hay peligro"
    (directions $? south $?)
    ?h <- (hice north)
    (percepts $? ?peligro $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    =>
    (retract ?h ?pasos)
    (assert (hice nada))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly south)
)

(defrule volverSouthWilly "Retrocede del sur si hay peligro"
    (directions $? north $?)
    ?h <- (hice south)
    (percepts $? ?peligro $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    =>
    (retract ?h ?pasos)
    (assert (hice nada))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly north)
)

(defrule volverEastWilly "Retrocede del este si hay peligro"
    (directions $? west $?)
    ?h <- (hice east)
    (percepts $? ?peligro $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    =>
    (retract ?h ?pasos)
    (assert (hice nada))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly west)
)

(defrule volverWestWilly "Retrocede del oeste si hay peligro"
    (directions $? east $?)
    ?h <- (hice west)
    (percepts $? ?peligro $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    =>
    (retract ?h ?pasos)
    (assert (hice nada))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly east)
)