; ===============================================
; SISTEMA EXPERTO WILLY EN EL ESPACIO - VERSION RESOLUTIVA
; Objetivo: RESOLVER MAPAS encontrando la Tierra para obtener 500+ puntos
; Estrategia: Navegación directa y eficiente hacia la Tierra
; ===============================================

(deffacts estadoInicial
    (hice nada)                    ; Último movimiento realizado
    (pasosDados 0)                 ; Contador de pasos actual
    (pasosMax 1000)                ; Máximo de pasos permitidos
    (mision buscar-tierra)         ; Misión principal: encontrar la Tierra
    (movimientos-consecutivos-este 0)  ; Contador para evitar ciclos
    (movimientos-consecutivos-norte 0) ; Contador para evitar ciclos
)

; ===============================================
; REGLAS DE DETECCIÓN DE LA TIERRA (MÁXIMA PRIORIDAD)
; ===============================================

(defrule tierraEncontrada "¡MISIÓN COMPLETADA! Tierra encontrada"
    (declare (salience 1000))
    (percepts $? Earth $?)
    ?mision <- (mision buscar-tierra)
    =>
    (retract ?mision)
    (assert (mision completada))
    (printout t "¡¡¡TIERRA ENCONTRADA!!! MISION COMPLETADA" crlf)
)

; ===============================================
; REGLAS DE NAVEGACIÓN DIRECTA HACIA LA TIERRA
; ===============================================

(defrule navegacionDirectaEste "Navegación directa hacia el ESTE para encontrar la Tierra"
    (declare (salience 80))
    (mision buscar-tierra)
    (not (percepts $? Pull $?))    ; No hay agujero negro
    (not (percepts $? Noise $?))   ; No hay alienígena
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? east $?)
    ?contador <- (movimientos-consecutivos-este ?count&:(< ?count 15))  ; Límite para evitar ciclos
    =>
    (retract ?h ?pasos ?contador)
    (assert (hice east))
    (assert (pasosDados (+ ?n 1)))
    (assert (movimientos-consecutivos-este (+ ?count 1)))
    (assert (movimientos-consecutivos-norte 0))  ; Reset contador norte
    (moveWilly east)
)

(defrule navegacionDirectaNorte "Navegación directa hacia el NORTE para encontrar la Tierra"
    (declare (salience 79))
    (mision buscar-tierra)
    (not (percepts $? Pull $?))
    (not (percepts $? Noise $?))
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? north $?)
    (not (directions $? east $?))  ; No puede ir al este
    ?contador <- (movimientos-consecutivos-norte ?count&:(< ?count 15))  ; Límite para evitar ciclos
    =>
    (retract ?h ?pasos ?contador)
    (assert (hice north))
    (assert (pasosDados (+ ?n 1)))
    (assert (movimientos-consecutivos-norte (+ ?count 1)))
    (assert (movimientos-consecutivos-este 0))  ; Reset contador este
    (moveWilly north)
)

; ===============================================
; REGLAS DE SEGURIDAD (PRIORIDAD ALTA)
; ===============================================

(defrule dispararLaser "Dispara cuando escucha al alienígena"
    (declare (salience 100))
    (hasLaser)
    (percepts $? Noise $?)
    (directions $? ?direction $?)
    =>
    (fireLaser ?direction)
)

(defrule escaparPeligroInmediato "Escapa inmediatamente del agujero negro"
    (declare (salience 90))
    (percepts $? Pull $?)
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? ?direccionSegura $?)
    =>
    (retract ?h ?pasos)
    (assert (hice ?direccionSegura))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly ?direccionSegura)
)

; ===============================================
; REGLAS DE EXPLORACIÓN ALTERNATIVA (CUANDO HAY LÍMITES)
; ===============================================

(defrule exploracionAlternativaSur "Explora hacia el SUR cuando se alcanzan límites"
    (declare (salience 70))
    (mision buscar-tierra)
    (not (percepts $? Pull $?))
    (not (percepts $? Noise $?))
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? south $?)
    (movimientos-consecutivos-este ?countE&:(>= ?countE 15))  ; Ha alcanzado límite este
    =>
    (retract ?h ?pasos)
    (assert (hice south))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly south)
)

(defrule exploracionAlternativaOeste "Explora hacia el OESTE cuando se alcanzan límites"
    (declare (salience 69))
    (mision buscar-tierra)
    (not (percepts $? Pull $?))
    (not (percepts $? Noise $?))
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? west $?)
    (movimientos-consecutivos-norte ?countN&:(>= ?countN 15))  ; Ha alcanzado límite norte
    =>
    (retract ?h ?pasos)
    (assert (hice west))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly west)
)

(defrule resetearContadores "Resetea contadores cuando cambia de dirección"
    (declare (salience 85))
    (hice ?direction&:(or (eq ?direction south) (eq ?direction west)))
    ?contE <- (movimientos-consecutivos-este ?countE&:(> ?countE 0))
    ?contN <- (movimientos-consecutivos-norte ?countN&:(> ?countN 0))
    =>
    (retract ?contE ?contN)
    (assert (movimientos-consecutivos-este 0))
    (assert (movimientos-consecutivos-norte 0))
)

; ===============================================
; REGLAS DE BÚSQUEDA DIRECTA OPTIMIZADA
; ===============================================

; Estrategia optimizada: Alternar entre ESTE y NORTE para llegar eficientemente
; a la esquina superior derecha donde típicamente está la Tierra

(defrule busquedaDirectaEste "Búsqueda directa hacia el ESTE (prioridad alta)"
    (declare (salience 75))
    (estrategia busqueda-directa)
    (not (percepts $? Pull $?))    ; No hay agujero negro
    (not (percepts $? Noise $?))   ; No hay alienígena
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? east $?)
    =>
    (retract ?h ?pasos)
    (assert (hice east))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly east)
)

(defrule busquedaDirectaNorte "Búsqueda directa hacia el NORTE cuando no puede ir al este"
    (declare (salience 74))
    (estrategia busqueda-directa)
    (not (percepts $? Pull $?))
    (not (percepts $? Noise $?))
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? north $?)
    (not (directions $? east $?))  ; No puede ir al este
    =>
    (retract ?h ?pasos)
    (assert (hice north))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly north)
)

; Reglas de exploración alternativa cuando la búsqueda directa no es posible
(defrule exploracionAlternativaSur "Explora hacia el SUR cuando no puede ir al este ni norte"
    (declare (salience 73))
    (not (percepts $? Pull $?))
    (not (percepts $? Noise $?))
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? south $?)
    (not (directions $? east $?))
    (not (directions $? north $?))
    =>
    (retract ?h ?pasos)
    (assert (hice south))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly south)
)

(defrule exploracionAlternativaOeste "Explora hacia el OESTE como última opción"
    (declare (salience 72))
    (not (percepts $? Pull $?))
    (not (percepts $? Noise $?))
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? west $?)
    (not (directions $? east $?))
    (not (directions $? north $?))
    (not (directions $? south $?))
    =>
    (retract ?h ?pasos)
    (assert (hice west))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly west)
)

; ===============================================
; REGLA DE EMERGENCIA
; ===============================================

; Regla de emergencia cuando hay ruido pero no láser
(defrule moverConRuido "Mueve cuidadosamente cuando hay ruido pero no láser"
    (declare (salience 80))
    (not (hasLaser))
    (percepts $? Noise $?)
    (not (percepts $? Pull $?))
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? ?direction $?)
    =>
    (retract ?h ?pasos)
    (assert (hice ?direction))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly ?direction)
)

; ===============================================
; FIN DEL SISTEMA EXPERTO WILLY OPTIMIZADO
; ===============================================