; ===============================================
; SISTEMA EXPERTO WILLY EN EL ESPACIO - VERSION FINAL OPTIMIZADA
; Objetivo: Alcanzar 500+ puntos encontrando la Tierra eficientemente
; Estrategia: Búsqueda directa hacia esquina superior derecha (donde suele estar la Tierra)
; ===============================================

(deffacts estadoInicial
    (hice nada)                    ; Último movimiento realizado
    (pasosDados 0)                 ; Contador de pasos actual
    (pasosMax 1000)                ; Máximo de pasos permitidos
    (estrategia busqueda-directa)  ; Estrategia de búsqueda directa
)

; ===============================================
; REGLAS DE MEMORIA SIMPLIFICADA
; ===============================================

(defrule marcarMovimiento "Marca cada movimiento realizado para evitar ciclos"
    (declare (salience 95))
    (hice ?direction&:(neq ?direction nada))
    (not (movimiento-realizado ?direction))
    =>
    (assert (movimiento-realizado ?direction))
)

; ===============================================
; REGLAS PRINCIPALES - PRIORIDAD ALTA
; ===============================================

; Regla 1: Disparar láser cuando hay ruido de alienígena (MÁXIMA PRIORIDAD)
(defrule dispararLaser "Dispara cuando escucha al alienígena"
    (declare (salience 100))
    (hasLaser)
    (percepts $? Noise $?)
    (directions $? ?direction $?)
    =>
    (fireLaser ?direction)
)

; Regla 2: Escapar cuando hay peligro inmediato (ALTA PRIORIDAD)
(defrule escaparPeligroInmediato "Escapa inmediatamente del agujero negro"
    (declare (salience 90))
    (percepts $? Pull $?)
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? ?direccionSegura $?)
    =>
    (retract ?h ?pasos)
    (assert (hice ?direccionSegura))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly ?direccionSegura)
)

; ===============================================
; REGLAS DE BÚSQUEDA DIRECTA OPTIMIZADA
; ===============================================

; Estrategia optimizada: Alternar entre ESTE y NORTE para llegar eficientemente
; a la esquina superior derecha donde típicamente está la Tierra

(defrule busquedaDirectaEste "Búsqueda directa hacia el ESTE (prioridad alta)"
    (declare (salience 75))
    (estrategia busqueda-directa)
    (not (percepts $? Pull $?))    ; No hay agujero negro
    (not (percepts $? Noise $?))   ; No hay alienígena
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? east $?)
    =>
    (retract ?h ?pasos)
    (assert (hice east))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly east)
)

(defrule busquedaDirectaNorte "Búsqueda directa hacia el NORTE cuando no puede ir al este"
    (declare (salience 74))
    (estrategia busqueda-directa)
    (not (percepts $? Pull $?))
    (not (percepts $? Noise $?))
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? north $?)
    (not (directions $? east $?))  ; No puede ir al este
    =>
    (retract ?h ?pasos)
    (assert (hice north))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly north)
)

; Reglas de exploración alternativa cuando la búsqueda directa no es posible
(defrule exploracionAlternativaSur "Explora hacia el SUR cuando no puede ir al este ni norte"
    (declare (salience 73))
    (not (percepts $? Pull $?))
    (not (percepts $? Noise $?))
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? south $?)
    (not (directions $? east $?))
    (not (directions $? north $?))
    =>
    (retract ?h ?pasos)
    (assert (hice south))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly south)
)

(defrule exploracionAlternativaOeste "Explora hacia el OESTE como última opción"
    (declare (salience 72))
    (not (percepts $? Pull $?))
    (not (percepts $? Noise $?))
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? west $?)
    (not (directions $? east $?))
    (not (directions $? north $?))
    (not (directions $? south $?))
    =>
    (retract ?h ?pasos)
    (assert (hice west))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly west)
)

; ===============================================
; REGLA DE EMERGENCIA
; ===============================================

; Regla de emergencia cuando hay ruido pero no láser
(defrule moverConRuido "Mueve cuidadosamente cuando hay ruido pero no láser"
    (declare (salience 80))
    (not (hasLaser))
    (percepts $? Noise $?)
    (not (percepts $? Pull $?))
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? ?direction $?)
    =>
    (retract ?h ?pasos)
    (assert (hice ?direction))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly ?direction)
)

; ===============================================
; FIN DEL SISTEMA EXPERTO WILLY OPTIMIZADO
; ===============================================