; ===============================================
; SISTEMA EXPERTO WILLY EN EL ESPACIO - VERSION MEJORADA
; Objetivo: Alcanzar 500+ puntos navegando eficientemente
; ===============================================

(deffacts estadoInicial
    (hice nada)                    ; Último movimiento realizado
    (pasosDados 0)                 ; Contador de pasos actual
    (pasosMax 1000)                ; Máximo de pasos permitidos
)

; ===============================================
; REGLAS PRINCIPALES - PRIORIDAD ALTA
; ===============================================

; Regla 1: Disparar láser cuando hay ruido de alienígena (MÁXIMA PRIORIDAD)
(defrule dispararLaser "Dispara cuando escucha al alienígena"
    (declare (salience 100))
    (hasLaser)
    (percepts $? Noise $?)
    (directions $? ?direction $?)
    =>
    (fireLaser ?direction)
)

; Regla 2: Escapar cuando hay peligro inmediato (ALTA PRIORIDAD)
(defrule escaparPeligroInmediato "Escapa inmediatamente del agujero negro"
    (declare (salience 90))
    (percepts $? Pull $?)
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? ?direccionSegura $?)
    =>
    (retract ?h ?pasos)
    (assert (hice ?direccionSegura))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly ?direccionSegura)
)

; ===============================================
; REGLAS DE MOVIMIENTO SEGURO - PRIORIDAD MEDIA
; ===============================================

; Regla 3: Movimiento al ESTE cuando es completamente seguro
(defrule moverEsteSeguro "Mueve al este cuando es seguro"
    (declare (salience 50))
    (not (percepts $? Pull $?))    ; No hay agujero negro
    (not (percepts $? Noise $?))   ; No hay alienígena
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? east $?)
    =>
    (retract ?h ?pasos)
    (assert (hice east))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly east)
)

; Regla 4: Movimiento al NORTE si no puede ir al este
(defrule moverNorteSeguro "Mueve al norte si no puede ir al este"
    (declare (salience 40))
    (not (percepts $? Pull $?))
    (not (percepts $? Noise $?))
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? north $?)
    (not (directions $? east $?))
    =>
    (retract ?h ?pasos)
    (assert (hice north))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly north)
)

; Regla 5: Movimiento al SUR si no puede ir al este ni norte
(defrule moverSurSeguro "Mueve al sur si no puede ir al este ni norte"
    (declare (salience 30))
    (not (percepts $? Pull $?))
    (not (percepts $? Noise $?))
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? south $?)
    (not (directions $? east $?))
    (not (directions $? north $?))
    =>
    (retract ?h ?pasos)
    (assert (hice south))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly south)
)

; Regla 6: Movimiento al OESTE como última opción
(defrule moverOesteSeguro "Mueve al oeste como última opción"
    (declare (salience 20))
    (not (percepts $? Pull $?))
    (not (percepts $? Noise $?))
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? west $?)
    (not (directions $? east $?))
    (not (directions $? north $?))
    (not (directions $? south $?))
    =>
    (retract ?h ?pasos)
    (assert (hice west))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly west)
)

; Regla 7: Movimiento de emergencia cuando hay ruido pero no láser
(defrule moverConRuido "Mueve cuidadosamente cuando hay ruido pero no láser"
    (declare (salience 80))
    (not (hasLaser))
    (percepts $? Noise $?)
    (not (percepts $? Pull $?))
    ?h <- (hice $?)
    ?pasos <- (pasosDados ?n)
    (pasosMax ?max&:(< ?n ?max))
    (directions $? ?direction $?)
    =>
    (retract ?h ?pasos)
    (assert (hice ?direction))
    (assert (pasosDados (+ ?n 1)))
    (moveWilly ?direction)
)

; ===============================================
; FIN DEL SISTEMA EXPERTO WILLY
; ===============================================