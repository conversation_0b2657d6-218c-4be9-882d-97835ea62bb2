Distribuci�n de "Willy en el espacio" 
======================================
Pr�cticas de Sistemas Inteligentes (Grado en Ingenier�a Inform�tica, UCO)
Por: <PERSON>, <PERSON> y <PERSON> G�mez


Quick start:
------------
 - Copia el contenido del directorio correspondiente a tu sistema operativo en el mismo directorio donde se encuentre el fichero "WillyDemo.jar"
 - Escribe en el terminal: java -jar WillyDemo.jar
 - Pulsa el bot�n "All maps" para resolver todos los mapas existentes en el directorio "maps"
 
 Objetivo Principal:

<PERSON> debe navegar un mapa de M x N casillas para encontrar el planeta Tierra.
Entorno y Peligros:

El mapa contiene agujeros negros; entrar en uno resulta en la muerte de Willy (fin de la partida).
Hay un único alienígena maligno en el mapa; encontrarse con él es mortal (fin de la partida).
<PERSON> no conoce la ubicación inicial de los peligros ni de otros ítems; debe explorar el mapa.
Los ítems del mapa no son visibles hasta que Willy visita su casilla.
Percepciones de Willy:

Al estar junto a una casilla con un agujero negro, Willy percibe una "fuerza de atracción". Este hecho se representa como (percepts Pull).

Al estar junto al alienígena, Willy percibe un "ruido característico". Este hecho se representa como (percepts Noise).

Si está junto a ambos, percibirá ambas cosas, representadas como (percepts Noise Pull) o (percepts Pull Noise).
Acciones de Willy:

Movimiento: Willy puede moverse a las casillas situadas arriba, abajo, izquierda o derecha, una casilla por movimiento. No puede moverse en diagonal ni más de una casilla. La acción se realiza con (moveWilly <dirección>).

Láser: Willy dispone de un láser que puede disparar una vez. 

El objetivo del láser es matar al alienígena.
El disparo recorre la fila o columna seleccionada en la dirección indicada, hasta salir del mapa o alcanzar al alienígena.
Una vez disparado, Willy no podrá volver a usarlo.
Si el alienígena es alcanzado, muere, el ruido cesa y su casilla se vuelve segura.
La acción se realiza con (fireLaser <dirección>).
La disponibilidad del láser se indica con el hecho (hasLaser).
Condiciones de Fin de Partida Adicionales:

Willy muere si realiza 1000 o más movimientos debido a la reserva limitada de oxígeno.
Sistema CLIPS y Programación:

Se debe programar un sistema basado en reglas en CLIPS para decidir los movimientos de Willy.
El sistema CLIPS proporcionará automáticamente los siguientes hechos: 
(directions <direcciones>): Indica las direcciones posibles para moverse o disparar desde la casilla actual (ej. north, south, east, west). No estarán todas disponibles si Willy está en un borde del mapa.

(hasLaser): Indica si el láser está disponible.
(percepts <percepción>): Indica las percepciones actuales (Pull, Noise, o ambas).
No se deben modificar estos hechos proporcionados por el sistema.
Tarea del programador: Crear hechos para guardar información sobre casillas exploradas y sus percepciones para tomar decisiones informadas.
Puntuación (para referencia de rendimiento):

Si encuentra la Tierra: 1000 - (movimientos) + (casillas diferentes visitadas). Se busca encontrar la Tierra pronto sin revisitar muchas casillas.

Si no encuentra la Tierra: (10 * casillas nuevas recorridas) - (movimientos). Se valora la exploración.

Si muere y no encontró la Tierra: Se restan 1000 puntos adicionales al cálculo anterior.